package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.pmw790.power.functions.Utilities;
import com.pmw790.power.functions.ConnectionRegistry;

import java.util.*;

/**
 * Abstract base class for power diagram contexts.
 * Contains shared functionality between RoomDiagramContext and CabinetDiagramContext.
 * This class manages common operations like property caching, constraint property management,
 * and value property handling.
 */
public abstract class PowerDiagramContext {

    // Core shared fields
    protected final Project project;
    protected final ConnectionRegistry registry;
    protected final String contextName;

    // Shared caching structures
    protected final Map<String, Map<String, Property>> constraintPropertiesCache; // Element Name -> (Constraint Type -> Property)
    protected final Map<String, Map<String, Property>> valuePropertiesCache; // Element Name -> (Property Name -> Property)
    protected final Map<String, Property> externalLoadPropertyCache; // Element Name -> external_load Property

    /**
     * Constructor for the abstract base class
     *
     * @param project The MagicDraw project
     * @param registry The connection registry
     * @param contextName The name of this context (room name or cabinet name)
     */
    protected PowerDiagramContext(Project project, ConnectionRegistry registry, String contextName) {
        this.project = project;
        this.registry = registry;
        this.contextName = contextName;
        this.constraintPropertiesCache = new HashMap<>();
        this.valuePropertiesCache = new HashMap<>();
        this.externalLoadPropertyCache = new HashMap<>();
    }

    // Abstract methods that must be implemented by subclasses

    /**
     * Gets the main block element for this context (room block or cabinet block)
     * @return The block element
     */
    public abstract Class getContextBlock();

    /**
     * Gets the list of power providers in this context
     * @return List of provider names
     */
    public abstract List<String> getProviders();

    /**
     * Gets a part property by name from this context
     * @param propertyName The property name
     * @return The Property object, or null if not found
     */
    public abstract Property getPartProperty(String propertyName);

    // Shared getter methods

    /**
     * Gets the project
     * @return The MagicDraw project
     */
    public Project getProject() {
        return project;
    }

    /**
     * Gets the connection registry
     * @return The connection registry
     */
    public ConnectionRegistry getConnectionRegistry() {
        return registry;
    }

    /**
     * Gets the context name (room name or cabinet name)
     * @return The context name
     */
    public String getContextName() {
        return contextName;
    }

    // Shared constraint property management methods

    /**
     * Caches constraint properties for a specific element
     * @param elementName The element name (provider name)
     * @param constraintType The type of constraint (e.g., "Power_Total")
     * @param property The constraint property
     */
    public void cacheConstraintProperty(String elementName, String constraintType, Property property) {
        if (elementName != null && constraintType != null && property != null) {
            // Get or create the map for this element
            Map<String, Property> elementConstraints = constraintPropertiesCache.computeIfAbsent(
                elementName, k -> new HashMap<>());

            // Store the constraint property
            elementConstraints.put(constraintType, property);
        }
    }

    /**
     * Gets all constraint properties for a specific element
     * @param elementName The element name (provider name)
     * @return Map of constraint type to constraint property, or empty map if none found
     */
    public Map<String, Property> getConstraintProperties(String elementName) {
        return constraintPropertiesCache.getOrDefault(elementName, new HashMap<>());
    }

    /**
     * Caches external load property for a specific element
     * @param elementName The element name (provider name)
     * @param property The external load property
     */
    public void cacheExternalLoadProperty(String elementName, Property property) {
        if (elementName != null && property != null) {
            externalLoadPropertyCache.put(elementName, property);
        }
    }

    /**
     * Gets external load property for a specific element
     * @param elementName The element name (provider name)
     * @return The external load property, or null if not found
     */
    public Property getExternalLoadProperty(String elementName) {
        return externalLoadPropertyCache.get(elementName);
    }

    // Shared value property management methods

    /**
     * Caches value properties for a specific element
     * @param elementName The element name
     * @param propertyName The property name
     * @param property The value property
     */
    public void cacheValueProperty(String elementName, String propertyName, Property property) {
        if (elementName != null && propertyName != null && property != null) {
            // Get or create the map for this element
            Map<String, Property> elementProperties = valuePropertiesCache.computeIfAbsent(
                elementName, k -> new HashMap<>());

            // Store the value property
            elementProperties.put(propertyName, property);
        }
    }

    /**
     * Gets all value properties for a specific element
     * @param elementName The element name
     * @return Map of property name to property, or empty map if none found
     */
    public Map<String, Property> getValueProperties(String elementName) {
        return valuePropertiesCache.getOrDefault(elementName, new HashMap<>());
    }

    /**
     * Gets a specific value property for an element
     * @param elementName The element name
     * @param propertyName The property name
     * @return The value property, or null if not found
     */
    public Property getValueProperty(String elementName, String propertyName) {
        Map<String, Property> elementProperties = valuePropertiesCache.get(elementName);
        if (elementProperties != null) {
            return elementProperties.get(propertyName);
        }
        return null;
    }

    // Shared utility methods

    /**
     * Logs a message with context information
     * @param message The message to log
     */
    protected void Log(String message) {
        Utilities.Log(getClass().getSimpleName() + " [" + contextName + "]: " + message);
    }

    /**
     * Creates a constraint property name for a given element and constraint type
     * @param elementName The element name
     * @param constraintType The constraint type
     * @return The constraint property name
     */
    protected String createConstraintPropertyName(String elementName, String constraintType) {
        return constraintType.toLowerCase() + "_" + elementName;
    }

    /**
     * Creates an external load property name for a given element
     * @param elementName The element name
     * @return The external load property name
     */
    protected String createExternalLoadPropertyName(String elementName) {
        return "external_load_" + elementName;
    }
}
